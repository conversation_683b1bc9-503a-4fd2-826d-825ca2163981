class ApplicationController < ActionController::Base
  include Pagy::Backend

  def current_user
    Current.user
  end

  before_action :set_current_request_details
  before_action :authenticate

  before_action :check_verification
  before_action :check_impersonation_timeout
  before_action :require_onboarding_completion
  before_action :set_current_organization
  before_action :require_organization_selected # Remove :logout since it doesn't exist
  before_action :check_impersonation_restrictions

  private

  def require_onboarding_completion
    return unless Current.user
    return if controller_path.start_with?('identity/')
    return if controller_name == 'onboarding'
    return if controller_name == 'registrations'

    # Allow access to launchpad regardless of onboarding state
    return if controller_name == 'launchpad'

    # Determine if access should be granted based on completed onboarding
    onboarding_ok =
      if Current.user.onboarding_completed?
        # General (Scout) onboarding done?
        true
      elsif controller_path.start_with?('talent/') &&
            Current.user.talent_signup_completed?
        # Accessing Talent area and Talent signup done?
        true
      else
        false # Neither general nor specific onboarding is complete for this area
      end

    return if onboarding_ok # Allow access if onboarding is considered complete for the context

    # If onboarding is not complete, redirect to the new onboarding flow
    # Always start with the welcome screen for incomplete onboarding
    redirect_to onboarding_welcome_path,
                alert: 'Please complete your setup to continue'
  end

  def check_verification
    return unless Current.user
    return if controller_path.start_with?('identity/')
    return if controller_name == 'registrations'

    if !Current.user.verified?
      sign_out
      redirect_to sign_in_path,
                  alert: 'Please verify your email before continuing'
    end
  end

  def check_impersonation_timeout
    return unless Current.impersonating?
    return if controller_path.start_with?('super_admin/')
    return if controller_name == 'sessions'

    # Check if impersonation has expired (24 hours)
    if Current.impersonation_expired?
      # End the impersonation log
      log = Current.impersonation_log
      log&.end_impersonation!

      # Clear impersonation session data
      session.delete(:original_session_token)
      session.delete(:impersonator_id)
      session.delete(:impersonation_log_id)

      # Clear Current attributes
      Current.impersonator_id = nil
      Current.impersonation_log_id = nil

      # Destroy current session and redirect to sign in
      Current.session&.destroy
      cookies.delete(:session_token)

      redirect_to sign_in_path,
                  alert:
                    'Impersonation session has expired. Please sign in again.'
    end
  end

  def authenticate
    if session_record = Session.find_by_id(cookies.signed[:session_token])
      Current.session = session_record
      Current.user = session_record.user # Explicitly assign/reload Current.user

      # Set impersonation attributes from session if available
      if session[:impersonator_id] && session[:impersonation_log_id]
        # Verify the impersonator still exists before setting impersonation state
        if User.exists?(session[:impersonator_id])
          Current.impersonator_id = session[:impersonator_id]
          Current.impersonation_log_id = session[:impersonation_log_id]
        else
          # Clear stale impersonation data
          Rails
            .logger.warn 'Clearing stale impersonation data - impersonator no longer exists'
          session.delete(:original_session_token)
          session.delete(:impersonator_id)
          session.delete(:impersonation_log_id)
        end
      end
    else
      redirect_to sign_in_path
    end
  end

  def sign_out
    cookies.delete(:session_token)
    Current.session&.destroy
  end

  def set_current_request_details
    Current.user_agent = request.user_agent
    Current.ip_address = request.ip
  end

  # Add these methods to your ApplicationController
  # (You'll need to integrate this with your existing code)

  def set_current_organization
    return unless Current.user

    # 1. Prioritize last logged-in organization
    if Current.user.last_logged_in_organization_id
      org =
        Current.user.organizations.find_by(
          id: Current.user.last_logged_in_organization_id,
        )
      if org
        Current.organization = org
        session[:organization_id] = org.id
        return # Found the org, no need for further checks
      else
        # Last logged-in org is invalid (e.g., user removed), clear the stored ID
        # Use update_column to skip validations/callbacks if necessary, though update should be fine here.
        Current.user.update(last_logged_in_organization_id: nil)
      end
    end

    # 2. Check session if last logged-in org wasn't found or set
    if session[:organization_id]
      org = Current.user.organizations.find_by(id: session[:organization_id])
      if org
        Current.organization = org

        # No need to set session[:organization_id] again, it's already there
        return # Found the org, no need for further checks
      else
        # Session org is invalid, clear it
        session.delete(:organization_id)
      end
    end

    # 3. Auto-set if user has exactly one org and none is selected yet
    if Current.organization.nil? && Current.user.organizations.count == 1
      org = Current.user.organizations.first
      Current.organization = org
      session[:organization_id] = org.id
    end
    # (If multiple orgs and none selected via last_logged_in or session, Current.organization remains nil)
  end

  def require_organization_selected
    return unless Current.user

    # Only enforce organization selection within the Scout namespace
    return unless controller_path.start_with?('scout/')

    return if Current.organization.present? # already selected

    redirect_to organizations_path, alert: 'Please choose an organization'
  end

  def check_impersonation_restrictions
    return unless Current.impersonating?

    # Prevent nested impersonation FIRST (before other checks)
    if controller_path.start_with?('super_admin/') &&
         controller_name == 'masquerades'
      redirect_to root_path,
                  alert:
                    'Cannot start impersonation while already impersonating.'
      return
    end

    # Skip restrictions for super admin controllers (except masquerades)
    return if controller_path.start_with?('super_admin/')

    # Skip restrictions for identity controllers (password reset, etc.)
    return if controller_path.start_with?('identity/')

    # Skip restrictions for sessions controller (allow sign out)
    return if controller_name == 'sessions'

    # List of restricted actions during impersonation
    restricted_actions = {
      'account_profiles' => %w[edit update],
      'identity/emails' => %w[edit update],
      'identity/password_resets' => %w[new create edit update],
      'registrations' => %w[new create],
      'organizations' => %w[new create edit update destroy],
      'scout/settings' => %w[show edit update],
      'talent/settings' => %w[show edit update],
    }

    # Check if current controller/action is restricted
    controller_key = controller_path
    if restricted_actions[controller_key]&.include?(action_name)
      redirect_to root_path,
                  alert:
                    'This action is not allowed during impersonation for security reasons.'
      return
    end
  end
end
