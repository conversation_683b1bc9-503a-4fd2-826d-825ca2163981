class OnboardingController < ApplicationController
  layout 'onboarding_modal'
  before_action :authenticate
  before_action :require_email_verification
  before_action :redirect_if_onboarded
  skip_before_action :require_onboarding_completion

  # Welcome modal - Value proposition screen
  def welcome
    @user = Current.user

    # Initialize session tracking for onboarding progress
    session[:onboarding_started_at] ||= Time.current
  end

  # Screen 1: Combined Organization Setup
  def organization_setup
    @user = Current.user
    @organization = Organization.new

    # Set onboarding context for validations
    @organization.onboarding_context = true
  end

  # Screen 2: Team Setup (Optional)
  def team_setup
    @user = Current.user
    @organization = current_organization
    @team_members = []
  end

  # Screen 3: Completion with confetti
  def completion
    @user = Current.user
    @organization = current_organization
  end

  # Handle organization setup form submission
  def create_organization
    @user = Current.user
    @organization = Organization.new(organization_params)
    @organization.onboarding_context = true

    if @organization.save
      # Create organization membership with owner role
      OrganizationMembership.create!(
        user: @user,
        organization: @organization,
        org_role: 'owner',
      )

      # Set as current organization
      Current.organization = @organization
      session[:organization_id] = @organization.id

      # Update user's onboarding step
      @user.update!(onboarding_step: 'team_setup')

      respond_to do |format|
        format.turbo_stream do
          render turbo_stream:
                   turbo_stream.replace(
                     'onboarding-content',
                     partial: 'team_setup',
                   )
        end
        format.html { redirect_to onboarding_team_setup_path }
      end
    else
      respond_to do |format|
        format.turbo_stream do
          render turbo_stream:
                   turbo_stream.replace(
                     'onboarding-content',
                     partial: 'organization_setup',
                   )
        end
        format.html do
          render :organization_setup, status: :unprocessable_entity
        end
      end
    end
  end

  # Handle team member invitations
  def invite_team_members
    @user = Current.user
    @organization = current_organization

    email_addresses = params[:team_emails]&.split(',')&.map(&:strip)&.reject(&:blank?) || []

    if email_addresses.any?
      email_addresses.each do |email|
        # Create user if doesn't exist, then create membership
        invited_user = User.find_or_create_by(email: email) do |user|
          user.password = SecureRandom.base58(12)
          user.verified = false
        end

        # Create organization membership
        unless @organization.users.include?(invited_user)
          OrganizationMembership.create!(
            user: invited_user,
            organization: @organization,
            org_role: 'member'
          )

          # Send invitation email
          UserMailer.with(
            invited_user: invited_user,
            organization: @organization,
            inviter: @user
          ).team_invitation.deliver_later
        end
      end
    end

    # Move to completion screen
    @user.update!(onboarding_step: 'completion')

    respond_to do |format|
      format.turbo_stream { render turbo_stream: turbo_stream.replace('onboarding-content', partial: 'completion') }
      format.html { redirect_to onboarding_completion_path }
    end
  end

  # Skip team setup and go to completion
  def skip_team_setup
    @user = Current.user
    @user.update!(onboarding_step: 'completion')

    respond_to do |format|
      format.turbo_stream { render turbo_stream: turbo_stream.replace('onboarding-content', partial: 'completion') }
      format.html { redirect_to onboarding_completion_path }
    end
  end

  # Complete onboarding and redirect to chosen path
  def complete_onboarding
    @user = Current.user

    # Mark onboarding as complete using the new helper method
    @user.complete_onboarding!
    @user.update!(scout_signup_completed: true)

    # Clear onboarding session data
    session.delete(:onboarding_started_at)

    # Redirect based on user choice
    redirect_path = case params[:choice]
                   when 'find_talent'
                     scout_talent_index_path
                   when 'post_job'
                     new_scout_job_path
                   else
                     scout_root_path
                   end

    redirect_to redirect_path, notice: 'Welcome to Ghostwrote! 🎉'
  end

  private

  def organization_params
    params.require(:organization).permit(:name, :email, :operating_timezone, :size, :industry_type, :logo)
  end

  def require_email_verification
    unless Current.user.verified?
      redirect_to root_path, alert: 'Please verify your email first'
    end
  end

  def redirect_if_onboarded
    # Redirect if onboarding is already completed
    if Current.user.onboarding_completed? || Current.user.talent_signup_completed?
      redirect_to launchpad_path
    end
  end

  def current_organization
    Current.organization || Current.user.organizations.first
  end
end
