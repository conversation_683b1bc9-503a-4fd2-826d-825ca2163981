import { Controller } from "@hotwired/stimulus";

// Onboarding Flow Controller
// Handles navigation and state management for the 3-screen onboarding flow
export default class extends Controller {
  static targets = ["step", "progressIndicator"];
  static values = { 
    currentStep: { type: Number, default: 0 },
    totalSteps: { type: Number, default: 4 }
  };

  connect() {
    console.log("OnboardingFlow controller connected");
    this.updateProgressIndicator();
    
    // Track onboarding analytics
    this.trackOnboardingStep();
  }

  // Navigate to next step
  nextStep(event) {
    if (event) {
      event.preventDefault();
    }
    
    console.log("Moving to next step from:", this.currentStepValue);
    
    // Track step completion
    this.trackStepCompletion(this.currentStepValue);
    
    // Let the form submission or link handle the actual navigation
    // This method is mainly for analytics and UI feedback
  }

  // Navigate to previous step
  previousStep(event) {
    if (event) {
      event.preventDefault();
    }
    
    console.log("Moving to previous step from:", this.currentStepValue);
    
    // Handle back navigation based on current step
    switch (this.currentStepValue) {
      case 1: // From organization setup to welcome
        window.location.href = '/onboarding/welcome';
        break;
      case 2: // From team setup to organization setup
        window.location.href = '/onboarding/organization_setup';
        break;
      case 3: // From completion to team setup
        window.location.href = '/onboarding/team_setup';
        break;
      default:
        console.log("No previous step available");
    }
  }

  // Update progress indicator
  updateProgressIndicator() {
    const progressContainer = document.getElementById('progress-indicator');
    if (!progressContainer) return;

    const steps = [
      { label: "Account", completed: true },
      { label: "Company", completed: this.currentStepValue > 0 },
      { label: "Team", completed: this.currentStepValue > 1 },
      { label: "Complete", completed: this.currentStepValue > 2 }
    ];

    progressContainer.innerHTML = steps.map((step, index) => {
      const isActive = index === this.currentStepValue;
      const isCompleted = step.completed && !isActive;
      
      let classes = "flex items-center justify-center w-6 h-6 rounded-full text-xs font-semibold";
      
      if (isCompleted) {
        classes += " bg-emerald-500 text-white";
      } else if (isActive) {
        classes += " bg-stone-900 text-white";
      } else {
        classes += " bg-stone-200 text-stone-400";
      }

      const icon = isCompleted ? 
        '<svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>' :
        (index + 1).toString();

      return `
        <div class="flex items-center ${index > 0 ? 'ml-4' : ''}">
          ${index > 0 ? '<div class="w-8 h-px bg-stone-300 mr-4"></div>' : ''}
          <div class="${classes}">
            ${icon}
          </div>
          <span class="ml-2 text-xs font-medium text-stone-600">${step.label}</span>
        </div>
      `;
    }).join('');
  }

  // Track onboarding analytics
  trackOnboardingStep() {
    if (typeof posthog !== 'undefined') {
      posthog.capture('onboarding_step_viewed', {
        step: this.currentStepValue,
        step_name: this.getStepName(this.currentStepValue),
        total_steps: this.totalStepsValue
      });
    }
  }

  // Track step completion
  trackStepCompletion(stepIndex) {
    if (typeof posthog !== 'undefined') {
      posthog.capture('onboarding_step_completed', {
        step: stepIndex,
        step_name: this.getStepName(stepIndex),
        total_steps: this.totalStepsValue
      });
    }
  }

  // Get human-readable step name
  getStepName(stepIndex) {
    const stepNames = ['welcome', 'organization_setup', 'team_setup', 'completion'];
    return stepNames[stepIndex] || 'unknown';
  }

  // Handle form validation feedback
  showValidationError(message) {
    // Create or update validation message
    let errorContainer = document.getElementById('onboarding-validation-error');
    
    if (!errorContainer) {
      errorContainer = document.createElement('div');
      errorContainer.id = 'onboarding-validation-error';
      errorContainer.className = 'mb-4 p-4 bg-red-50 border border-red-200 rounded-lg';
      
      // Insert at the top of the form
      const form = this.element.querySelector('form');
      if (form) {
        form.insertBefore(errorContainer, form.firstChild);
      }
    }
    
    errorContainer.innerHTML = `
      <div class="flex items-center">
        <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
        </svg>
        <span class="text-sm text-red-700">${message}</span>
      </div>
    `;
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
      if (errorContainer && errorContainer.parentNode) {
        errorContainer.remove();
      }
    }, 5000);
  }

  // Clear validation errors
  clearValidationErrors() {
    const errorContainer = document.getElementById('onboarding-validation-error');
    if (errorContainer) {
      errorContainer.remove();
    }
  }
}
