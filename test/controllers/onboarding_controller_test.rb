require "test_helper"

class OnboardingControllerTest < ActionDispatch::IntegrationTest
  setup do
    @user = users(:scout_user)
    @user.update!(verified: true, onboarding_completed: false, onboarding_step: 'personal')
    sign_in @user
  end

  # Welcome screen tests
  test "should get welcome screen" do
    get onboarding_welcome_path
    assert_response :success
    assert_select "h1", text: "Let's get started"
    assert_select "a[href='#{onboarding_organization_setup_path}']"
  end

  test "should redirect to launchpad if onboarding already completed" do
    @user.update!(onboarding_completed: true)
    get onboarding_welcome_path
    assert_redirected_to launchpad_path
  end

  test "should redirect to launchpad if talent signup completed" do
    @user.update!(talent_signup_completed: true)
    get onboarding_welcome_path
    assert_redirected_to launchpad_path
  end

  test "should redirect to root if email not verified" do
    @user.update!(verified: false)
    get onboarding_welcome_path
    assert_redirected_to root_path
    assert_equal "Please verify your email first", flash[:alert]
  end

  # Organization setup tests
  test "should get organization setup screen" do
    get onboarding_organization_setup_path
    assert_response :success
    assert_select "h1", text: "Set up your company"
    assert_select "form[action='#{onboarding_create_organization_path}']"
  end

  test "should create organization with valid params" do
    assert_difference('Organization.count') do
      post onboarding_create_organization_path, params: {
        organization: {
          name: "Test Company",
          email: "<EMAIL>",
          operating_timezone: "America/New_York",
          size: "medium",
          industry_type: "Technology"
        }
      }
    end

    organization = Organization.last
    assert_equal "Test Company", organization.name
    assert_equal "<EMAIL>", organization.email
    assert_equal "Technology", organization.industry_type

    # Should create organization membership
    membership = OrganizationMembership.find_by(user: @user, organization: organization)
    assert_not_nil membership
    assert_equal "owner", membership.org_role

    # Should update user's onboarding step
    @user.reload
    assert_equal "team_setup", @user.onboarding_step
  end

  test "should not create organization with invalid params" do
    assert_no_difference('Organization.count') do
      post onboarding_create_organization_path, params: {
        organization: {
          name: "", # Required field missing
          email: "invalid-email", # Invalid email format
          operating_timezone: "",
          size: ""
        }
      }
    end

    assert_response :unprocessable_entity
  end

  test "should handle turbo stream response for organization creation" do
    post onboarding_create_organization_path, 
         params: {
           organization: {
             name: "Test Company",
             email: "<EMAIL>",
             operating_timezone: "America/New_York",
             size: "medium"
           }
         },
         headers: { "Accept" => "text/vnd.turbo-stream.html" }

    assert_response :success
    assert_match "turbo-stream", response.content_type
  end

  # Team setup tests
  test "should get team setup screen" do
    organization = create_organization_for_user(@user)
    get onboarding_team_setup_path
    assert_response :success
    assert_select "h1", text: "Invite your team"
    assert_select "form[action='#{onboarding_invite_team_members_path}']"
  end

  test "should invite team members with valid emails" do
    organization = create_organization_for_user(@user)
    
    assert_difference('User.count', 2) do
      assert_difference('OrganizationMembership.count', 2) do
        post onboarding_invite_team_members_path, params: {
          team_emails: "<EMAIL>, <EMAIL>"
        }
      end
    end

    # Should update user's onboarding step
    @user.reload
    assert_equal "completion", @user.onboarding_step

    # Should create users and memberships
    john = User.find_by(email: "<EMAIL>")
    sarah = User.find_by(email: "<EMAIL>")
    
    assert_not_nil john
    assert_not_nil sarah
    assert_equal false, john.verified
    assert_equal false, sarah.verified

    assert organization.users.include?(john)
    assert organization.users.include?(sarah)
  end

  test "should skip team setup" do
    organization = create_organization_for_user(@user)
    
    assert_no_difference('User.count') do
      post onboarding_skip_team_setup_path
    end

    @user.reload
    assert_equal "completion", @user.onboarding_step
  end

  test "should handle empty team emails" do
    organization = create_organization_for_user(@user)
    
    assert_no_difference('User.count') do
      post onboarding_invite_team_members_path, params: {
        team_emails: ""
      }
    end

    @user.reload
    assert_equal "completion", @user.onboarding_step
  end

  # Completion screen tests
  test "should get completion screen" do
    organization = create_organization_for_user(@user)
    @user.update!(onboarding_step: 'completion')
    
    get onboarding_completion_path
    assert_response :success
    assert_select "h1", text: "You're all set! 🎉"
  end

  test "should complete onboarding and redirect to post job" do
    organization = create_organization_for_user(@user)
    @user.update!(onboarding_step: 'completion')
    
    post onboarding_complete_path, params: { choice: 'post_job' }
    
    @user.reload
    assert @user.onboarding_completed?
    assert @user.scout_signup_completed?
    assert_not_nil @user.onboarding_completed_at
    assert_equal "completed", @user.onboarding_step
    
    assert_redirected_to new_scout_job_path
    assert_equal "Welcome to Ghostwrote! 🎉", flash[:notice]
  end

  test "should complete onboarding and redirect to find talent" do
    organization = create_organization_for_user(@user)
    @user.update!(onboarding_step: 'completion')
    
    post onboarding_complete_path, params: { choice: 'find_talent' }
    
    @user.reload
    assert @user.onboarding_completed?
    assert @user.scout_signup_completed?
    
    assert_redirected_to scout_talent_index_path
  end

  test "should complete onboarding and redirect to dashboard by default" do
    organization = create_organization_for_user(@user)
    @user.update!(onboarding_step: 'completion')
    
    post onboarding_complete_path, params: { choice: 'dashboard' }
    
    @user.reload
    assert @user.onboarding_completed?
    
    assert_redirected_to scout_root_path
  end

  private

  def create_organization_for_user(user)
    organization = Organization.create!(
      name: "Test Organization",
      email: "<EMAIL>",
      operating_timezone: "America/New_York",
      size: "medium"
    )
    
    OrganizationMembership.create!(
      user: user,
      organization: organization,
      org_role: 'owner'
    )
    
    Current.organization = organization
    organization
  end
end
